# File Copy Logic Improvements

## Summary of Changes

This document outlines the improvements made to the file copy logic in the download worker to better handle shared files and avoid redundant downloads.

## Issues Fixed

### 1. ✅ Duplicate File Warning Message
**Problem**: Duplicate files were being logged as errors instead of warnings.

**Solution**: Changed the log level from `console.error` to `console.warn` for SKIP_DUPLICATE_FILE messages.

**Location**: `src/services/download-worker.js` line 275-280

**Before**:
```javascript
console.error(`❌ Failed to download ${downloadItem.name}:`, error.message);
```

**After**:
```javascript
if (error.message.startsWith('SKIP_DUPLICATE_FILE')) {
    console.warn(`⚠️ Skipping duplicate file: ${downloadItem.name} - ${error.message}`);
    // ... handle skipping logic
    return;
}
console.error(`❌ Failed to download ${downloadItem.name}:`, error.message);
```

### 2. ✅ Database-Based File Copy Logic
**Problem**: The original logic used an in-memory registry (`FilePathRegistry`) that gets reset on application restart, missing opportunities to copy files that were already downloaded by other users.

**Solution**: Implemented database-based file copy detection that queries the `scanned_files_cuong` table to find files that have already been downloaded.

## New Implementation Details

### Database-Based Copy Detection

The new `findCopyableFileInDatabase()` method uses two strategies:

#### Strategy 1: Exact File ID Match (True Shared Files)
```sql
SELECT * FROM scanned_files_cuong 
WHERE file_id = ? 
  AND user_email != ? 
  AND local_path IS NOT NULL
```

This finds files that are truly shared (same Google Drive file_id) across different users.

#### Strategy 2: Similar File Match
```sql
SELECT * FROM scanned_files_cuong 
WHERE name = ? 
  AND size = ? 
  AND mime_type = ? 
  AND user_email != ? 
  AND local_path IS NOT NULL
```

This finds files with the same name, size, and type that might be duplicates.

### Benefits

1. **Persistent Detection**: Works across application restarts
2. **Database-Driven**: Uses the actual database state instead of in-memory cache
3. **Two-Level Strategy**: Handles both true shared files and similar files
4. **File Verification**: Checks that source files still exist on disk before copying
5. **Better Logging**: Shows which copy method was used (same_file_id vs similar_file)

## Database Examples Found

### Shared Files in Database
The analysis found examples of files that exist across multiple users:

#### Example 1: "teca" Folder
- **File**: `teca` (folder)
- **Users**: `<EMAIL>`, `<EMAIL>`
- **Type**: Different file_ids but same name/size (similar files)
- **Permissions**: Shows extensive sharing permissions across the organization

#### Example 2: "Bảng tính chưa có tiêu đề" (Untitled Spreadsheet)
- **File**: Default Google Sheets
- **Users**: `<EMAIL>`, `<EMAIL>`
- **Type**: Different file_ids but same name/size
- **Size**: 1024 bytes each

## Current Database State

- **Total Files**: 1000 files scanned
- **Downloaded Files**: 0 (no files have local_path yet)
- **Shared Files by file_id**: 0 (no true shared files found)
- **Similar Files**: 2 groups found

## Testing

Created test scripts to verify the new logic:
- `query-shared-files.js`: Analyzes database for shared files
- `test-database-copy-logic.js`: Tests the new copy detection logic

## Future Improvements

1. **Performance**: Add database indexes on commonly queried fields
2. **Metrics**: Track copy success rates and time savings
3. **Cleanup**: Remove dependency on `FilePathRegistry` once fully migrated
4. **Validation**: Add file hash comparison for better duplicate detection

## Migration Notes

The old `FilePathRegistry` is still used for registration purposes but the critical copy detection logic now uses the database. This ensures:

- ✅ Copy detection works across application restarts
- ✅ Real-time database state is used
- ✅ Better handling of shared files
- ✅ Reduced redundant downloads
- ✅ Improved logging and debugging

## Usage

The improved logic is automatically used when downloading files. When a file is about to be downloaded, the system will:

1. Check if the same user already downloaded this exact file
2. Check if another user has downloaded the same file (by file_id)
3. Check if another user has downloaded a similar file (name+size+type)
4. If found, copy the existing file instead of downloading
5. Log the copy method used for debugging

This significantly reduces download time and bandwidth usage for shared files.
