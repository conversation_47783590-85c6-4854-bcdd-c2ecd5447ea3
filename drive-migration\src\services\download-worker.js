/**
 * Download Worker
 * Worker x<PERSON> lý download đồng thời từ Google Drive
 */

import fs from 'fs';
import path from 'path';
import { pipeline } from 'stream/promises';
import { createWriteStream } from 'fs';
import EventEmitter from 'events';
import { googleDriveAPI } from '../api/google-drive-api.js';
import { filePathRegistry } from './file-path-registry.js';

export class DownloadWorker extends EventEmitter {
    constructor(sessionId, sessionConfig, supabase) {
        super();
        this.sessionId = sessionId;
        this.sessionConfig = sessionConfig;
        this.supabase = supabase;
        this.isRunning = false;
        this.isPaused = false;
        this.isCancelled = false;
        this.activeDownloads = new Map(); // fileId -> download info
        this.stats = {
            downloadedFiles: 0,
            failedFiles: 0,
            skippedFiles: 0,
            downloadedSize: 0
        };
    }

    /**
     * Bắt đầu download process
     */
    async start() {
        if (this.isRunning) {
            throw new Error('Worker is already running');
        }

        this.isRunning = true;
        this.isPaused = false;
        this.isCancelled = false;

        try {
            console.log(`🚀 Starting download worker for session: ${this.sessionId}`);

            // Lấy danh sách files cần download từ scanned_files_cuong - fetch all records in batches
            let allDownloadItems = [];
            let hasMore = true;
            let offset = 0;
            const batchSize = 1000;

            // Determine order based on session processing_order
            let orderBy = 'created_at';
            let ascending = true;

            if (this.sessionConfig.processing_order === 'user_email') {
                orderBy = 'user_email';
            } else if (this.sessionConfig.processing_order === 'size_asc') {
                orderBy = 'size';
                ascending = true;
            } else if (this.sessionConfig.processing_order === 'size_desc') {
                orderBy = 'size';
                ascending = false;
            }

            while (hasMore) {
                const { data: batchItems, error } = await this.supabase.getServiceClient()
                    .from('scanned_files_cuong')
                    .select('*')
                    .in('user_email', this.sessionConfig.selected_users)
                    .or('download_status.is.null,download_status.eq.not_downloaded')
                    .order(orderBy, { ascending })
                    .range(offset, offset + batchSize - 1);

                if (error) {
                    throw new Error(`Failed to get files from scanned_files_cuong: ${error.message}`);
                }

                if (batchItems && batchItems.length > 0) {
                    // Filter out skipped MIME types
                    const filteredItems = batchItems.filter(item =>
                        !this.sessionConfig.skip_mime_types.includes(item.mime_type)
                    );
                    allDownloadItems.push(...filteredItems);
                    hasMore = batchItems.length === batchSize;
                    offset += batchSize;
                    console.log(`📄 Fetched files batch: ${filteredItems.length} files (Total so far: ${allDownloadItems.length})`);
                } else {
                    hasMore = false;
                }
            }

            const downloadItems = allDownloadItems;

            if (downloadItems.length === 0) {
                console.log('No files to download');
                await this.completeSession();
                return;
            }

            console.log(`📋 Found ${downloadItems.length} files to download`);

            // Tạo thư mục download path nếu chưa tồn tại
            if (!fs.existsSync(this.sessionConfig.download_path)) {
                fs.mkdirSync(this.sessionConfig.download_path, { recursive: true });
            }

            // Xử lý download với concurrency
            await this.processDownloads(downloadItems);

        } catch (error) {
            console.error('❌ Error in download worker:', error.message);
            await this.handleWorkerError(error);
            this.emit('error', error);
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Xử lý downloads với concurrency control
     */
    async processDownloads(downloadItems) {
        const concurrentDownloads = this.sessionConfig.concurrent_downloads || 3;
        const queue = [...downloadItems];
        const activePromises = [];
        let lastRetryCheck = Date.now();

        while (queue.length > 0 || activePromises.length > 0) {
            // Periodically check for retry items (every 5 seconds)
            if (Date.now() - lastRetryCheck > 5000) {
                await this.addRetryItemsToQueue(queue);
                lastRetryCheck = Date.now();
            }
            // Kiểm tra pause/cancel
            if (this.isPaused) {
                console.log('⏸️ Download paused, waiting...');
                await this.waitForResume();
            }

            if (this.isCancelled) {
                console.log('❌ Download cancelled');
                break;
            }

            // Bắt đầu downloads mới nếu có slot
            while (activePromises.length < concurrentDownloads && queue.length > 0) {
                const item = queue.shift();
                const promise = this.downloadFile(item)
                    .then(() => {
                        // Remove from active promises
                        const index = activePromises.indexOf(promise);
                        if (index > -1) {
                            activePromises.splice(index, 1);
                        }
                    })
                    .catch((error) => {
                        console.error(`Error downloading ${item.file_name}:`, error.message);

                        // If stop_on_error is enabled and this is a critical error, stop the process
                        if (this.sessionConfig.stop_on_error && error.message.includes('Stopping download process')) {
                            this.isCancelled = true;
                            throw error; // Re-throw to stop the entire process
                        }

                        // Remove from active promises
                        const index = activePromises.indexOf(promise);
                        if (index > -1) {
                            activePromises.splice(index, 1);
                        }
                    });

                activePromises.push(promise);
            }

            // Chờ ít nhất 1 download hoàn thành
            if (activePromises.length > 0) {
                await Promise.race(activePromises);
            }

            // Emit progress
            await this.emitProgress();
        }

        // Chờ tất cả downloads hoàn thành
        await Promise.all(activePromises);

        // Kiểm tra xem còn pending items với retry_count > 0 không
        let hasRetryItems = true;
        while (hasRetryItems && !this.isCancelled) {
            const { data: retryItems, error } = await this.supabase.getServiceClient()
                .from('download_items')
                .select('id')
                .eq('download_session_id', this.sessionId)
                .eq('status', 'pending')
                .gt('retry_count', 0);

            if (error) {
                console.error('Error checking retry items:', error.message);
                break;
            }

            if (retryItems && retryItems.length > 0) {
                console.log(`⏳ Waiting for ${retryItems.length} retry items...`);
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Add retry items back to queue and process them
                const retryQueue = [];
                await this.addRetryItemsToQueue(retryQueue);
                if (retryQueue.length > 0) {
                    await this.processDownloads(retryQueue);
                }
            } else {
                hasRetryItems = false;
            }
        }

        if (!this.isCancelled) {
            await this.completeSession();
        }
    }

    /**
     * Download một file
     */
    async downloadFile(downloadItem) {
        const startTime = Date.now();

        try {
            // Update status to downloading
            await this.updateDownloadItemStatus(downloadItem.id, 'downloading', {
                download_started_at: new Date().toISOString()
            });

            // Update session current file
            await this.updateSessionCurrentFile(downloadItem.user_email, downloadItem.name);

            console.log(`📥 Downloading: ${downloadItem.name} (${downloadItem.user_email})`);

            // Xử lý folder
            if (downloadItem.mime_type === 'application/vnd.google-apps.folder') {
                await this.createFolder(downloadItem);

                // Update stats for folder completion
                this.stats.downloadedFiles++;
                console.log(`✅ Created folder: ${downloadItem.name}`);

                // Emit progress after folder creation
                await this.emitProgress();
                return;
            }

            // Download file
            await this.downloadFileContent(downloadItem);

            // Update success status
            const duration = Date.now() - startTime;
            await this.updateDownloadItemStatus(downloadItem.id, 'completed', {
                download_completed_at: new Date().toISOString(),
                download_duration: duration
            });

            this.stats.downloadedFiles++;
            this.stats.downloadedSize += downloadItem.size;

            console.log(`✅ Downloaded: ${downloadItem.name} (${duration}ms)`);

            // Emit progress after each successful download
            await this.emitProgress();

        } catch (error) {
            // Xử lý trường hợp skip duplicate file
            if (error.message.startsWith('SKIP_DUPLICATE_FILE')) {
                console.warn(`⚠️ Skipping duplicate file: ${downloadItem.name} - ${error.message}`);

                // Update status to skipped
                await this.updateDownloadItemStatus(downloadItem.id, 'skipped', {
                    error_message: error.message,
                    download_completed_at: new Date().toISOString()
                });

                // Emit progress after skipping
                await this.emitProgress();
                return;
            }

            console.error(`❌ Failed to download ${downloadItem.name}:`, error.message);

            // Log error to session error log if continue_on_error is enabled
            if (this.sessionConfig.continue_on_error) {
                await this.logErrorToSession(downloadItem, error);
            }

            // Handle retry logic - simplified since we don't track retry_count in scanned_files_cuong
            const maxRetries = this.sessionConfig.max_retries || 3;

            // For now, mark as failed immediately since retry logic is complex without retry_count field
            // In a production system, you might want to add a retry_count field to scanned_files_cuong
            console.log(`❌ Marking ${downloadItem.name} as failed (retry logic simplified)`);
            await this.updateDownloadItemStatus(downloadItem.id, 'failed', {
                error_message: error.message
            });

            this.stats.failedFiles++;

            // Emit progress after failed download
            await this.emitProgress();

            // If stop_on_error is enabled, throw error to stop the entire process
            if (this.sessionConfig.stop_on_error) {
                throw new Error(`Download failed for ${downloadItem.name}: ${error.message}. Stopping download process.`);
            }
        }
    }

    /**
     * Tạo folder
     */
    async createFolder(downloadItem) {
        try {
            // Tạo folder structure từ full_path
            const folderPath = this.buildLocalPath(downloadItem.file_path, downloadItem.user_email);

            if (!fs.existsSync(folderPath)) {
                fs.mkdirSync(folderPath, { recursive: true });
            }

            // Update local path
            await this.updateDownloadItemStatus(downloadItem.id, 'completed', {
                local_path: folderPath,
                download_completed_at: new Date().toISOString()
            });

            // No need to update separate scanned_files table - we work directly with scanned_files_cuong

            console.log(`📁 Created folder: ${folderPath}`);

        } catch (error) {
            throw new Error(`Failed to create folder: ${error.message}`);
        }
    }

    /**
     * Download file content
     */
    async downloadFileContent(downloadItem) {
        try {
            // Check if this exact file has already been downloaded by this user
            const { data: existingRecord, error: existingError } = await this.supabase.getServiceClient()
                .from('scanned_files_cuong')
                .select('local_path, size')
                .eq('file_id', downloadItem.file_id)
                .eq('user_email', downloadItem.user_email)
                .not('local_path', 'is', null)
                .single();

            if (!existingError && existingRecord && fs.existsSync(existingRecord.local_path)) {
                console.log(`⏭️ File already downloaded by this user, skipping: ${downloadItem.name}`);
                console.log(`   Local path: ${existingRecord.local_path}`);

                // Update download item status to completed if not already
                await this.updateDownloadItemStatus(downloadItem.id, 'completed', {
                    local_path: existingRecord.local_path,
                    download_completed_at: new Date().toISOString()
                });

                return;
            }

            // Check if file already exists in another account (can copy instead of download)
            const copyableFileInfo = await this.findCopyableFileInDatabase(downloadItem);

            if (copyableFileInfo) {
                console.log(`🔄 File exists in another account, copying instead of downloading...`);
                const copyResult = await this.copyFileFromExistingAccount(downloadItem, copyableFileInfo);
                return copyResult;
            }

            const isGoogleDoc = googleDriveAPI.isGoogleDocsFormat(downloadItem.mime_type);

            // For Google Docs, export to downloadable format instead of just exposing
            if (isGoogleDoc) {
                return await this.downloadGoogleDoc(downloadItem);
            }

            // Build local file path từ full_path - download it to user_email dir
            let localPath = this.buildLocalPath(downloadItem.full_path, downloadItem.user_email);
            const localDir = path.dirname(localPath);

            // Tạo thư mục nếu chưa tồn tại
            if (!fs.existsSync(localDir)) {
                fs.mkdirSync(localDir, { recursive: true });
            }

            // Handle file name conflicts
            const finalPath = this.handleFileNameConflict(localPath, downloadItem.size || 0);

            // Download file từ Google Drive
            const fileStream = await this.getFileStream(downloadItem);
            const writeStream = createWriteStream(finalPath);

            // Stream file to local
            await pipeline(fileStream, writeStream);

            // Update download status, download file path and downloaded at (like the logic before)
            await this.updateDownloadItemStatus(downloadItem.id, 'downloaded', {
                local_path: finalPath,
                downloaded_at: new Date().toISOString()
            });

            // Register downloaded file in registry
            filePathRegistry.registerDownloadedFile(
                downloadItem.full_path,
                downloadItem.user_email,
                finalPath,
                downloadItem.size || 0
            );

        } catch (error) {
            throw new Error(`Failed to download file content: ${error.message}`);
        }
    }

    /**
     * Download Google Doc by exporting to appropriate format
     */
    async downloadGoogleDoc(downloadItem) {
        try {
            // Check if file already exists in another account (can copy instead of download)
            const existingFileInfo = await this.findCopyableFileInDatabase(downloadItem);

            if (existingFileInfo) {
                console.log(`🔄 Google Doc exists in another account, copying instead of downloading...`);
                const copyResult = await this.copyFileFromExistingAccount(downloadItem, existingFileInfo);
                return copyResult;
            }

            // Define export formats for Google Docs
            const exportFormats = {
                'application/vnd.google-apps.document': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
                'application/vnd.google-apps.spreadsheet': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // XLSX
                'application/vnd.google-apps.presentation': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
                'application/vnd.google-apps.drawing': 'image/png', // PNG
                'application/vnd.google-apps.form': 'application/pdf' // PDF
            };

            const exportMimeType = exportFormats[downloadItem.mime_type];
            if (!exportMimeType) {
                // Fallback to expose if no export format available
                return await this.exposeGoogleDoc(downloadItem);
            }

            // Determine file extension
            const extensions = {
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
                'image/png': '.png',
                'application/pdf': '.pdf'
            };

            const fileExtension = extensions[exportMimeType] || '.txt';

            // Build local file path
            let localPath = this.buildLocalPath(downloadItem.file_path, downloadItem.user_email);

            // Add appropriate extension if not present
            if (!localPath.endsWith(fileExtension)) {
                localPath += fileExtension;
            }

            const localDir = path.dirname(localPath);

            // Create directory if not exists
            if (!fs.existsSync(localDir)) {
                fs.mkdirSync(localDir, { recursive: true });
            }

            // Handle file name conflicts
            const finalPath = this.handleFileNameConflict(localPath, downloadItem.file_size || 0);

            // Export file from Google Drive
            const fileStream = await googleDriveAPI.exportFile(downloadItem.user_email, downloadItem.file_id, exportMimeType);
            const writeStream = createWriteStream(finalPath);

            // Stream file to local
            await pipeline(fileStream, writeStream);

            // Update local path
            await this.updateDownloadItemStatus(downloadItem.id, null, {
                local_path: finalPath
            });

            // Register downloaded Google Doc in registry
            filePathRegistry.registerDownloadedFile(
                downloadItem.full_path,
                downloadItem.user_email,
                finalPath,
                downloadItem.size || 0
            );

            console.log(`📄 Downloaded Google Doc: ${downloadItem.name} -> ${finalPath}`);

            return {
                success: true,
                downloaded: true,
                localPath: finalPath,
                exportFormat: exportMimeType
            };

        } catch (error) {
            // Fallback to expose if export fails
            console.warn(`⚠️ Export failed for ${downloadItem.file_name}, falling back to expose: ${error.message}`);
            return await this.exposeGoogleDoc(downloadItem);
        }
    }

    /**
     * Expose Google Doc instead of downloading (fallback method)
     */
    async exposeGoogleDoc(downloadItem) {
        try {
            // Get file details to get webViewLink
            const fileDetails = await googleDriveAPI.getFile(downloadItem.user_email, downloadItem.file_id);

            // Update download status to exposed
            await this.updateDownloadItemStatus(downloadItem.id, null, {
                local_path: null,
                web_view_link: fileDetails.webViewLink,
                export_links: JSON.stringify(fileDetails.exportLinks || {})
            });

            console.log(`🔗 Exposed Google Doc: ${downloadItem.name} - ${fileDetails.webViewLink}`);

            return {
                success: true,
                exposed: true,
                webViewLink: fileDetails.webViewLink,
                exportLinks: fileDetails.exportLinks || {}
            };

        } catch (error) {
            throw new Error(`Failed to expose Google Doc: ${error.message}`);
        }
    }

    /**
     * Lấy file stream từ Google Drive
     */
    async getFileStream(downloadItem) {
        try {
            const isGoogleDoc = googleDriveAPI.isGoogleDocsFormat(downloadItem.mime_type);

            if (isGoogleDoc) {
                // For Google Docs, use expose approach instead of download
                throw new Error(`EXPOSE_REQUIRED:${downloadItem.file_id}`);
            } else {
                // Download binary file
                return await googleDriveAPI.downloadFileStream(downloadItem.user_email, downloadItem.file_id);
            }

        } catch (error) {
            throw new Error(`Failed to get file stream: ${error.message}`);
        }
    }

    /**
     * Lấy export MIME type cho Google Docs
     */
    getExportMimeType(mimeType) {
        const exportMap = {
            'application/vnd.google-apps.document': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
            'application/vnd.google-apps.spreadsheet': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.google-apps.presentation': 'application/vnd.openxmlformats-officedocument.presentationml.presentation' // .pptx
        };

        return exportMap[mimeType] || 'application/pdf';
    }

    /**
     * Build local file path from full_path
     */
    buildLocalPath(fullPath, userEmail) {
        // Remove leading slash if exists
        let relativePath = fullPath.replace(/^\//, '');

        // Check if full_path already contains user email (shared files)
        const hasUserEmail = relativePath.includes('@');

        let pathParts;
        if (hasUserEmail) {
            // For shared files, use full_path as is
            pathParts = relativePath.split('/').map(part => this.sanitizeFileName(part));
        } else {
            // For user's own files, prepend user email
            const userFolder = this.sanitizeFileName(userEmail);
            pathParts = [userFolder, ...relativePath.split('/').map(part => this.sanitizeFileName(part))];
        }

        // Build full local path
        return path.join(this.sessionConfig.download_path, ...pathParts);
    }

    /**
     * Sanitize file name
     */
    sanitizeFileName(fileName) {
        return fileName
            .replace(/[<>:"/\\|?*]/g, '_')
            .replace(/\s+/g, ' ')
            .trim();
    }

    /**
     * Handle file name conflicts - check file size and skip or create suffix
     */
    handleFileNameConflict(filePath, expectedSize = 0) {
        if (!fs.existsSync(filePath)) {
            return filePath;
        }

        try {
            // Get existing file size
            const stats = fs.statSync(filePath);
            const existingSize = stats.size;

            // Calculate size difference percentage
            const sizeDifference = Math.abs(existingSize - expectedSize);
            const sizePercentage = expectedSize > 0 ? (sizeDifference / expectedSize) * 100 : 100;

            // If file size difference is less than 10%, skip download
            if (sizePercentage <= 10) {
                console.log(`⏭️ Skipping duplicate file (size similar): ${filePath} (existing: ${existingSize}, expected: ${expectedSize})`);
                this.stats.skippedFiles++;
                throw new Error(`SKIP_DUPLICATE_FILE: File already exists with similar size at ${filePath}`);
            }

            // File size difference is more than 10%, create new file with suffix
            console.log(`📝 File exists but size differs significantly (${sizePercentage.toFixed(1)}%): ${filePath}`);
            return this.generateUniqueFileName(filePath);

        } catch (error) {
            if (error.message.startsWith('SKIP_DUPLICATE_FILE')) {
                throw error;
            }
            // If we can't read the file, treat as conflict and create suffix
            console.warn(`⚠️ Cannot read existing file ${filePath}: ${error.message}`);
            return this.generateUniqueFileName(filePath);
        }
    }

    /**
     * Generate unique file name with suffix _1, _2, etc.
     */
    generateUniqueFileName(originalPath) {
        const dir = path.dirname(originalPath);
        const ext = path.extname(originalPath);
        const nameWithoutExt = path.basename(originalPath, ext);

        let counter = 1;
        let newPath;

        do {
            const newName = `${nameWithoutExt}_${counter}${ext}`;
            newPath = path.join(dir, newName);
            counter++;
        } while (fs.existsSync(newPath));

        console.log(`📝 Generated unique filename: ${newPath}`);
        return newPath;
    }

    /**
     * Update scanned file download status in scanned_files_cuong
     */
    async updateDownloadItemStatus(itemId, status, additionalFields = {}) {
        const updateData = { ...additionalFields };
        if (status) {
            updateData.download_status = status;
        }

        // Map additional fields to scanned_files_cuong schema
        if (additionalFields.download_started_at) {
            // No specific field for download_started_at in scanned_files_cuong, but we can use updated_at
            updateData.updated_at = additionalFields.download_started_at;
            delete updateData.download_started_at;
        }
        if (additionalFields.download_completed_at) {
            updateData.downloaded_at = additionalFields.download_completed_at;
            delete updateData.download_completed_at;
        }
        if (additionalFields.error_message) {
            updateData.download_error_message = additionalFields.error_message;
            delete updateData.error_message;
        }
        // Remove fields that don't exist in scanned_files_cuong
        delete updateData.download_duration;
        delete updateData.retry_count;
        delete updateData.web_view_link;
        delete updateData.export_links;

        await this.supabase.getServiceClient()
            .from('scanned_files_cuong')
            .update(updateData)
            .eq('id', itemId);
    }

    /**
     * Update session current file
     */
    async updateSessionCurrentFile(userEmail, fileName) {
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                current_user_email: userEmail,
                current_file_name: fileName
            })
            .eq('id', this.sessionId);
    }

    /**
     * Emit progress event
     */
    async emitProgress() {
        // Update session stats
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                downloaded_files: this.stats.downloadedFiles,
                failed_files: this.stats.failedFiles,
                downloaded_size: this.stats.downloadedSize
            })
            .eq('id', this.sessionId);

        this.emit('progress', {
            downloadedFiles: this.stats.downloadedFiles,
            failedFiles: this.stats.failedFiles,
            skippedFiles: this.stats.skippedFiles,
            downloadedSize: this.stats.downloadedSize
        });
    }

    /**
     * Add retry items back to queue - not needed with new workflow
     * Since we work directly with scanned_files_cuong, retry logic is handled differently
     */
    async addRetryItemsToQueue(queue) {
        // No longer needed with the new workflow
        // Retry logic is now handled by re-querying scanned_files_cuong for failed items
        console.log('🔄 Retry logic handled by re-querying scanned_files_cuong for failed items');
    }

    /**
     * Complete session
     */
    async completeSession() {
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                status: 'completed',
                completed_at: new Date().toISOString(),
                downloaded_files: this.stats.downloadedFiles,
                failed_files: this.stats.failedFiles,
                downloaded_size: this.stats.downloadedSize
            })
            .eq('id', this.sessionId);

        this.emit('completed', this.stats);
        console.log(`✅ Download session completed: ${this.sessionId}`);
    }

    /**
     * Handle worker error
     */
    async handleWorkerError(error) {
        await this.supabase.getServiceClient()
            .from('download_sessions')
            .update({
                status: 'failed',
                error_message: error.message,
                completed_at: new Date().toISOString()
            })
            .eq('id', this.sessionId);
    }

    /**
     * Log error to session error log for continue_on_error mode
     */
    async logErrorToSession(downloadItem, error) {
        try {
            // Get current session to read existing error log
            const { data: session, error: sessionError } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .select('error_log')
                .eq('id', this.sessionId)
                .single();

            if (sessionError) {
                console.error('Failed to get session for error logging:', sessionError.message);
                return;
            }

            // Create error entry
            const errorEntry = {
                timestamp: new Date().toISOString(),
                file_id: downloadItem.file_id,
                file_name: downloadItem.file_name,
                file_path: downloadItem.file_path,
                user_email: downloadItem.user_email,
                error_message: error.message,
                retry_count: downloadItem.retry_count || 0
            };

            // Add to existing error log
            const currentErrorLog = session.error_log || [];
            const updatedErrorLog = [...currentErrorLog, errorEntry];

            // Update session with new error log
            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    error_log: updatedErrorLog
                })
                .eq('id', this.sessionId);

            console.log(`📝 Logged error for ${downloadItem.file_name} to session error log`);
        } catch (logError) {
            console.error('Failed to log error to session:', logError.message);
        }
    }

    /**
     * Pause worker
     */
    async pause() {
        this.isPaused = true;
        console.log(`⏸️ Pausing download worker: ${this.sessionId}`);
    }

    /**
     * Resume worker
     */
    async resume() {
        this.isPaused = false;
        console.log(`▶️ Resuming download worker: ${this.sessionId}`);
    }

    /**
     * Cancel worker
     */
    async cancel() {
        this.isCancelled = true;
        console.log(`❌ Cancelling download worker: ${this.sessionId}`);
    }

    /**
     * Wait for resume
     */
    async waitForResume() {
        return new Promise((resolve) => {
            const checkResume = () => {
                if (!this.isPaused || this.isCancelled) {
                    resolve();
                } else {
                    setTimeout(checkResume, 1000);
                }
            };
            checkResume();
        });
    }

    /**
     * Find copyable file in database - check if same file exists in another user's account with local_path
     */
    async findCopyableFileInDatabase(downloadItem) {
        try {
            // Strategy 1: Look for exact same file_id in other users (true shared files)
            const { data: sameFileIdRecords, error: fileIdError } = await this.supabase.getServiceClient()
                .from('scanned_files_cuong')
                .select('id, file_id, name, user_email, full_path, local_path, size, mime_type')
                .eq('file_id', downloadItem.file_id)
                .neq('user_email', downloadItem.user_email)
                .not('local_path', 'is', null)
                .limit(1);

            if (fileIdError) {
                console.warn(`⚠️ Error querying same file_id: ${fileIdError.message}`);
            } else if (sameFileIdRecords && sameFileIdRecords.length > 0) {
                const sourceFile = sameFileIdRecords[0];
                console.log(`🎯 Found exact same file_id in another account: ${sourceFile.user_email}`);

                // Verify the local file still exists
                if (fs.existsSync(sourceFile.local_path)) {
                    return {
                        userEmail: sourceFile.user_email,
                        localPath: sourceFile.local_path,
                        fileSize: sourceFile.size || 0,
                        method: 'same_file_id'
                    };
                } else {
                    console.warn(`⚠️ Source file not found: ${sourceFile.local_path}`);
                }
            }

            // Strategy 2: Look for files with same name, size, and mime_type in other users
            const { data: similarFiles, error: similarError } = await this.supabase.getServiceClient()
                .from('scanned_files_cuong')
                .select('id, file_id, name, user_email, full_path, local_path, size, mime_type')
                .eq('name', downloadItem.name)
                .eq('size', downloadItem.size || 0)
                .eq('mime_type', downloadItem.mime_type)
                .neq('user_email', downloadItem.user_email)
                .not('local_path', 'is', null)
                .limit(1);

            if (similarError) {
                console.warn(`⚠️ Error querying similar files: ${similarError.message}`);
            } else if (similarFiles && similarFiles.length > 0) {
                const sourceFile = similarFiles[0];
                console.log(`📋 Found similar file in another account: ${sourceFile.user_email}`);

                // Verify the local file still exists
                if (fs.existsSync(sourceFile.local_path)) {
                    return {
                        userEmail: sourceFile.user_email,
                        localPath: sourceFile.local_path,
                        fileSize: sourceFile.size || 0,
                        method: 'similar_file'
                    };
                } else {
                    console.warn(`⚠️ Source file not found: ${sourceFile.local_path}`);
                }
            }

            return null; // No copyable file found

        } catch (error) {
            console.error(`❌ Error finding copyable file in database: ${error.message}`);
            return null;
        }
    }

    /**
     * Copy file từ account đã có sang account hiện tại
     */
    async copyFileFromExistingAccount(downloadItem, sourceFileInfo) {
        try {
            console.log(`📋 Copying file from existing account: ${downloadItem.file_name}`);
            console.log(`   Source: ${sourceFileInfo.userEmail} -> ${sourceFileInfo.localPath}`);
            console.log(`   Target: ${downloadItem.user_email}`);

            // Build target local path
            const targetLocalPath = this.buildLocalPath(downloadItem.full_path, downloadItem.user_email);
            const targetDir = path.dirname(targetLocalPath);

            // Tạo thư mục target nếu chưa tồn tại
            if (!fs.existsSync(targetDir)) {
                fs.mkdirSync(targetDir, { recursive: true });
            }

            // Handle file name conflicts cho target path
            const finalTargetPath = this.handleFileNameConflict(targetLocalPath, downloadItem.size || 0);

            // Verify source file exists
            if (!fs.existsSync(sourceFileInfo.localPath)) {
                throw new Error(`Source file not found: ${sourceFileInfo.localPath}`);
            }

            // Copy file
            await fs.promises.copyFile(sourceFileInfo.localPath, finalTargetPath);

            // Verify copy success
            const sourceStats = await fs.promises.stat(sourceFileInfo.localPath);
            const targetStats = await fs.promises.stat(finalTargetPath);

            if (sourceStats.size !== targetStats.size) {
                throw new Error(`Copy verification failed: size mismatch (source: ${sourceStats.size}, target: ${targetStats.size})`);
            }

            // Update local path in download item
            await this.updateDownloadItemStatus(downloadItem.id, null, {
                local_path: finalTargetPath
            });

            // Register copied file in registry
            filePathRegistry.registerCopiedFile(
                downloadItem.full_path,
                downloadItem.user_email,
                finalTargetPath,
                sourceFileInfo.userEmail,
                downloadItem.size || 0
            );

            console.log(`✅ Successfully copied file: ${finalTargetPath}`);
            console.log(`   📊 Saved download time by copying from ${sourceFileInfo.userEmail} (method: ${sourceFileInfo.method || 'unknown'})`);

            return {
                success: true,
                copied: true,
                localPath: finalTargetPath,
                sourceAccount: sourceFileInfo.userEmail,
                copyMethod: sourceFileInfo.method || 'unknown'
            };

        } catch (error) {
            throw new Error(`Failed to copy file from existing account: ${error.message}`);
        }
    }
}
