import { supabaseClient } from './src/database/supabase.js';

/**
 * Query shared files in scanned_files_cuong table
 * Find files that exist for multiple users (shared files)
 */
async function querySharedFiles() {
    try {
        console.log('🔍 Querying shared files in scanned_files_cuong table...');
        
        const client = supabaseClient.getServiceClient();
        
        // First, let's get a sample of files to understand the data structure
        const { data: sampleFiles, error: sampleError } = await client
            .from('scanned_files_cuong')
            .select(`
                id,
                file_id,
                name,
                user_email,
                full_path,
                size,
                mime_type,
                download_status,
                local_path,
                permissions,
                owners,
                created_at
            `)
            .limit(10);

        if (sampleError) {
            console.error('❌ Error querying sample files:', sampleError.message);
            return;
        }

        console.log('\n📋 Sample files from database:');
        sampleFiles.slice(0, 2).forEach((file, index) => {
            console.log(`\n--- Sample Record ${index + 1} ---`);
            console.log(JSON.stringify(file, null, 2));
        });

        // Query to find files that exist for multiple users (shared files)
        // This finds files with the same file_id but different user_email
        const { data: sharedFiles, error } = await client
            .from('scanned_files_cuong')
            .select(`
                id,
                file_id,
                name,
                user_email,
                full_path,
                size,
                mime_type,
                download_status,
                local_path,
                permissions,
                owners,
                created_at
            `)
            .order('file_id', { ascending: true })
            .order('user_email', { ascending: true });
            
        if (error) {
            console.error('❌ Error querying shared files:', error.message);
            return;
        }
        
        console.log(`📊 Total files found: ${sharedFiles.length}`);
        
        // Group files by file_id to find shared files
        const fileGroups = {};
        sharedFiles.forEach(file => {
            if (!fileGroups[file.file_id]) {
                fileGroups[file.file_id] = [];
            }
            fileGroups[file.file_id].push(file);
        });
        
        // Find files that appear for multiple users (same file_id)
        const sharedFileGroups = Object.entries(fileGroups)
            .filter(([, files]) => files.length > 1)
            .slice(0, 5); // Get first 5 shared file groups

        console.log(`🔗 Found ${sharedFileGroups.length} shared file groups by file_id (showing first 5)`);

        // Also look for files with same name and size across different users
        const nameGroups = {};
        sharedFiles.forEach(file => {
            const key = `${file.name}_${file.size}`;
            if (!nameGroups[key]) {
                nameGroups[key] = [];
            }
            nameGroups[key].push(file);
        });

        const similarFileGroups = Object.entries(nameGroups)
            .filter(([, files]) => {
                // Check if files are from different users
                const uniqueUsers = new Set(files.map(f => f.user_email));
                return uniqueUsers.size > 1;
            })
            .slice(0, 5);

        console.log(`🔗 Found ${similarFileGroups.length} file groups with same name+size across different users`);

        // Display examples from both types
        const allExamples = [
            ...sharedFileGroups.map(([fileId, files]) => ({ type: 'same_file_id', fileId, files })),
            ...similarFileGroups.map(([nameSize, files]) => ({ type: 'same_name_size', nameSize, files }))
        ].slice(0, 2);

        allExamples.forEach((example, i) => {
            console.log(`\n📁 Example ${i + 1} (${example.type}):`);
            if (example.type === 'same_file_id') {
                console.log(`File ID: ${example.fileId}`);
            } else {
                console.log(`Name+Size Key: ${example.nameSize}`);
            }
            console.log(`File Name: ${example.files[0].name}`);
            console.log(`Users who have this file:`);

            example.files.forEach((file, index) => {
                console.log(`\n--- Record ${index + 1} ---`);
                console.log(JSON.stringify({
                    id: file.id,
                    file_id: file.file_id,
                    name: file.name,
                    user_email: file.user_email,
                    full_path: file.full_path,
                    size: file.size,
                    mime_type: file.mime_type,
                    download_status: file.download_status,
                    local_path: file.local_path,
                    permissions: file.permissions,
                    owners: file.owners,
                    created_at: file.created_at
                }, null, 2));
            });
        });
        
        // Summary statistics
        console.log('\n📈 Summary Statistics:');
        console.log(`Total unique files: ${Object.keys(fileGroups).length}`);
        console.log(`Shared files (multiple users): ${sharedFileGroups.length}`);
        console.log(`Single-user files: ${Object.keys(fileGroups).length - sharedFileGroups.length}`);
        
        // Show distribution of shared files
        const distributionMap = {};
        sharedFileGroups.forEach(([fileId, files]) => {
            const userCount = files.length;
            distributionMap[userCount] = (distributionMap[userCount] || 0) + 1;
        });
        
        console.log('\n📊 Shared file distribution:');
        Object.entries(distributionMap)
            .sort(([a], [b]) => parseInt(a) - parseInt(b))
            .forEach(([userCount, fileCount]) => {
                console.log(`  ${userCount} users: ${fileCount} files`);
            });
            
    } catch (error) {
        console.error('❌ Error in querySharedFiles:', error.message);
    }
}

// Run the query
querySharedFiles()
    .then(() => {
        console.log('\n✅ Query completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('❌ Script failed:', error.message);
        process.exit(1);
    });
